{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/connect/components/theme/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/theme/theme-provider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qEACA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/connect/components/theme/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/theme/theme-provider.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,4WAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iDACA", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/connect/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from 'next';\nimport './globals.css';\n\nimport { GeistMono } from 'geist/font/mono';\nimport { GeistSans } from 'geist/font/sans';\nimport { ThemeProvider } from '@/components/theme/theme-provider';\n\nexport const metadata: Metadata = {\n  title: 'Rwanda Connect',\n  description: 'Connect with any service providers in rwanda easily',\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html\n      className={`${GeistSans.variable} ${GeistMono.variable}`}\n      lang=\"en\"\n      suppressHydrationWarning\n    >\n      <body>\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"system\"\n          disableTransitionOnChange\n          enableSystem\n        >\n          {children}\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AACA;AAAA;AACA;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,6WAAC;QACC,WAAW,GAAG,uSAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,uSAAA,CAAA,YAAS,CAAC,QAAQ,EAAE;QACxD,MAAK;QACL,wBAAwB;kBAExB,cAAA,6WAAC;sBACC,cAAA,6WAAC,yIAAA,CAAA,gBAAa;gBACZ,WAAU;gBACV,cAAa;gBACb,yBAAyB;gBACzB,YAAY;0BAEX;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/connect/node_modules/.pnpm/next%4015.4.6_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,uMACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/geist@1.4.2_next@15.4.6_rea_d5858ca71c15c943f46285ef41f25ff3/node_modules/geist/dist/geistmono_8e2790ea.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geistmono_8e2790ea-module__89BZZW__className\",\n  \"variable\": \"geistmono_8e2790ea-module__89BZZW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/connect/node_modules/.pnpm/geist%401.4.2_next%4015.4.6_rea_d5858ca71c15c943f46285ef41f25ff3/node_modules/geist/dist/geistmono_8e2790ea.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22mono.js%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/geist-mono/GeistMono-Variable.woff2%22,%22variable%22:%22--font-geist-mono%22,%22adjustFontFallback%22:false,%22fallback%22:[%22ui-monospace%22,%22SFMono-Regular%22,%22Roboto%20Mono%22,%22Menlo%22,%22Monaco%22,%22Liberation%20Mono%22,%22DejaVu%20Sans%20Mono%22,%22Courier%20New%22,%22monospace%22],%22weight%22:%22100%20900%22}],%22variableName%22:%22GeistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,2QAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,2QAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,2QAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/geist@1.4.2_next@15.4.6_rea_d5858ca71c15c943f46285ef41f25ff3/node_modules/geist/dist/geistsans_81192321.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geistsans_81192321-module__wxN7Lq__className\",\n  \"variable\": \"geistsans_81192321-module__wxN7Lq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/connect/node_modules/.pnpm/geist%401.4.2_next%4015.4.6_rea_d5858ca71c15c943f46285ef41f25ff3/node_modules/geist/dist/geistsans_81192321.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22sans.js%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/geist-sans/Geist-Variable.woff2%22,%22variable%22:%22--font-geist-sans%22,%22weight%22:%22100%20900%22}],%22variableName%22:%22GeistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'GeistSans', 'GeistSans Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,2QAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;IAEhB;AACJ;AAEA,IAAI,2QAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,2QAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}