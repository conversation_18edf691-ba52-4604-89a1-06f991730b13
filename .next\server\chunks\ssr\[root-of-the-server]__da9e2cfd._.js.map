{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/connect/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/connect/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  \"inline-flex shrink-0 items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium text-sm outline-none transition-all focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40',\n        outline:\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:border-input dark:bg-input/30 dark:hover:bg-input/50',\n        secondary:\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n        ghost:\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n        sm: 'h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5',\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n        icon: 'size-9',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : 'button';\n\n  return (\n    <Comp\n      className={cn(buttonVariants({ variant, size, className }))}\n      data-slot=\"button\"\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/connect/components/ui/mode-switcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useTheme } from 'next-themes';\nimport { useCallback } from 'react';\nimport { Button } from '@/components/ui/button';\n\nexport function ModeSwitcher() {\n  const { setTheme, resolvedTheme } = useTheme();\n\n  const toggleTheme = useCallback(() => {\n    setTheme(resolvedTheme === 'dark' ? 'light' : 'dark');\n  }, [resolvedTheme, setTheme]);\n\n  return (\n    <Button\n      className=\"group/toggle extend-touch-target size-8 cursor-pointer\"\n      onClick={toggleTheme}\n      size=\"icon\"\n      title=\"Toggle theme\"\n      variant=\"ghost\"\n    >\n      <svg\n        className=\"size-5\"\n        fill=\"none\"\n        height=\"24\"\n        stroke=\"currentColor\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        strokeWidth=\"2\"\n        viewBox=\"0 0 24 24\"\n        width=\"24\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n      >\n        <title>Toggle theme</title>\n        <path d=\"M0 0h24v24H0z\" fill=\"none\" stroke=\"none\" />\n        <path d=\"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0\" />\n        <path d=\"M12 3l0 18\" />\n        <path d=\"M12 9l4.65 -4.65\" />\n        <path d=\"M12 14.3l7.37 -7.37\" />\n        <path d=\"M12 19.6l8.85 -8.85\" />\n      </svg>\n      <span className=\"sr-only\">Toggle theme</span>\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,yPAAA,CAAA,WAAQ,AAAD;IAE3C,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,SAAS,kBAAkB,SAAS,UAAU;IAChD,GAAG;QAAC;QAAe;KAAS;IAE5B,qBACE,6WAAC,2HAAA,CAAA,SAAM;QACL,WAAU;QACV,SAAS;QACT,MAAK;QACL,OAAM;QACN,SAAQ;;0BAER,6WAAC;gBACC,WAAU;gBACV,MAAK;gBACL,QAAO;gBACP,QAAO;gBACP,eAAc;gBACd,gBAAe;gBACf,aAAY;gBACZ,SAAQ;gBACR,OAAM;gBACN,OAAM;;kCAEN,6WAAC;kCAAM;;;;;;kCACP,6WAAC;wBAAK,GAAE;wBAAgB,MAAK;wBAAO,QAAO;;;;;;kCAC3C,6WAAC;wBAAK,GAAE;;;;;;kCACR,6WAAC;wBAAK,GAAE;;;;;;kCACR,6WAAC;wBAAK,GAAE;;;;;;kCACR,6WAAC;wBAAK,GAAE;;;;;;kCACR,6WAAC;wBAAK,GAAE;;;;;;;;;;;;0BAEV,6WAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}]}